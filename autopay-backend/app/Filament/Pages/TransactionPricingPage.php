<?php

namespace App\Filament\Pages;

use App\Settings\TransactionPricingSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Halt;

class TransactionPricingPage extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static string $settings = TransactionPricingSettings::class;

    protected static ?string $navigationGroup = 'Cấu hình hệ thống';

    protected static ?string $title = 'Bảng giá giao dịch';

    protected static ?string $navigationLabel = 'Bảng giá giao dịch';

    protected static ?int $navigationSort = 2;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Bảng giá giao dịch')
                    ->description('Cấu hình các mức giá cho giao dịch theo số lượng')
                    ->schema([
                        Forms\Components\Repeater::make('pricing_tiers')
                            ->label('Các mức giá')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->label('Tên mức giá (Tiếng Việt)')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('name_en')
                                            ->label('Tên mức giá (Tiếng Anh)')
                                            ->required()
                                            ->maxLength(255),
                                    ]),
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('min_transactions')
                                            ->label('Số giao dịch tối thiểu')
                                            ->required()
                                            ->numeric()
                                            ->minValue(0),
                                        Forms\Components\TextInput::make('max_transactions')
                                            ->label('Số giao dịch tối đa')
                                            ->numeric()
                                            ->minValue(1)
                                            ->nullable()
                                            ->helperText('Để trống nếu không giới hạn'),
                                        Forms\Components\TextInput::make('price_per_transaction')
                                            ->label('Giá mỗi giao dịch (VNĐ)')
                                            ->required()
                                            ->numeric()
                                            ->minValue(0),
                                    ]),
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Select::make('color_class')
                                            ->label('Màu sắc hiển thị')
                                            ->options([
                                                'green' => 'Xanh lá (Miễn phí)',
                                                'blue' => 'Xanh dương',
                                                'yellow' => 'Vàng',
                                                'red' => 'Đỏ',
                                                'default' => 'Mặc định',
                                            ])
                                            ->required(),
                                        Forms\Components\Toggle::make('is_unlimited')
                                            ->label('Không giới hạn')
                                            ->helperText('Mức này áp dụng cho tất cả giao dịch còn lại'),
                                    ]),
                            ])
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? null)
                            ->addActionLabel('Thêm mức giá')
                            ->reorderable()
                            ->minItems(1)
                            ->maxItems(10)
                            ->defaultItems(0),
                    ]),

                Forms\Components\Section::make('Công cụ tính toán')
                    ->description('Tính toán chi phí cho số lượng giao dịch cụ thể')
                    ->schema([
                        Forms\Components\TextInput::make('test_transaction_count')
                            ->label('Số giao dịch để test')
                            ->numeric()
                            ->minValue(0)
                            ->live()
                            ->afterStateUpdated(function ($state, $set) {
                                if ($state) {
                                    $settings = app(TransactionPricingSettings::class);
                                    $calculation = $settings->calculateTotalCost((int) $state);

                                    $breakdown = collect($calculation['breakdown'])
                                        ->map(fn($item) => "{$item['tier_name']}: {$item['transactions']} giao dịch × " . number_format($item['price_per_transaction']) . "₫ = " . number_format($item['tier_cost']) . "₫")
                                        ->join("\n");

                                    $set('calculation_result', "Tổng chi phí: " . number_format($calculation['total_cost']) . "₫\n\nChi tiết:\n" . $breakdown);
                                }
                            }),
                        Forms\Components\Textarea::make('calculation_result')
                            ->label('Kết quả tính toán')
                            ->rows(6)
                            ->disabled(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('reset_to_default')
                ->label('Khôi phục mặc định')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->requiresConfirmation()
                ->modalHeading('Khôi phục bảng giá mặc định')
                ->modalDescription('Bạn có chắc chắn muốn khôi phục bảng giá về cài đặt mặc định? Tất cả thay đổi hiện tại sẽ bị mất.')
                ->modalSubmitActionLabel('Khôi phục')
                ->action(function () {
                    $settings = app(TransactionPricingSettings::class);
                    $defaultTiers = $settings->getDefaultPricingTiers();
                    $settings->setPricingTiers($defaultTiers);

                    // Refresh the form with default data
                    $this->fillForm();

                    Notification::make()
                        ->title('Khôi phục thành công')
                        ->body('Bảng giá đã được khôi phục về cài đặt mặc định.')
                        ->success()
                        ->send();
                }),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $settings = app(TransactionPricingSettings::class);
        $data['pricing_tiers'] = $settings->getPricingTiers();

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Validate tier logic before saving
        if (isset($data['pricing_tiers'])) {
            $this->validateTierLogic($data['pricing_tiers']);
        }

        return $data;
    }

    private function validateTierLogic(array $tiers): void
    {
        // Sort tiers by min_transactions
        usort($tiers, fn($a, $b) => $a['min_transactions'] <=> $b['min_transactions']);

        $expectedMin = 0;

        foreach ($tiers as $index => $tier) {
            // Check if min_transactions matches expected value
            if ($tier['min_transactions'] !== $expectedMin) {
                Notification::make()
                    ->title('Lỗi cấu hình')
                    ->body("Có khoảng trống trong bảng giá. Mức " . ($index + 1) . " phải bắt đầu từ {$expectedMin} giao dịch.")
                    ->danger()
                    ->send();

                throw new Halt();
            }

            // Check if max_transactions is valid
            if ($tier['max_transactions'] !== null && $tier['max_transactions'] < $tier['min_transactions']) {
                Notification::make()
                    ->title('Lỗi cấu hình')
                    ->body("Mức " . ($index + 1) . ": Số giao dịch tối đa phải lớn hơn hoặc bằng số giao dịch tối thiểu.")
                    ->danger()
                    ->send();

                throw new Halt();
            }

            // Update expected min for next tier
            if ($tier['is_unlimited'] || $tier['max_transactions'] === null) {
                // This should be the last tier
                if ($index !== count($tiers) - 1) {
                    Notification::make()
                        ->title('Lỗi cấu hình')
                        ->body("Chỉ mức cuối cùng mới có thể không giới hạn.")
                        ->danger()
                        ->send();

                    throw new Halt();
                }
                break;
            } else {
                $expectedMin = $tier['max_transactions'] + 1;
            }
        }
    }
}
