<?php

use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\TransactionPricingController;

/*
|--------------------------------------------------------------------------
| API Routes for Core Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Core module.
|
*/

// Public routes
Route::get('/transaction-pricing', [TransactionPricingController::class, 'index']);
Route::post('/transaction-pricing/calculate', [TransactionPricingController::class, 'calculateCost']);

Route::middleware(['auth:sanctum'])->group(function () {
    // Admin routes for managing transaction pricing
    Route::put('/transaction-pricing', [TransactionPricingController::class, 'update']);
    Route::post('/transaction-pricing/reset', [TransactionPricingController::class, 'resetToDefault']);
});
