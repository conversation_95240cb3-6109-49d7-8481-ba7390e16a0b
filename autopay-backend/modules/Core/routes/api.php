<?php

use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\TransactionPricingController;

/*
|--------------------------------------------------------------------------
| API Routes for Core Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Core module.
|
*/

// Public routes
Route::get('/transaction-pricing', [TransactionPricingController::class, 'index']);
Route::post('/transaction-pricing/calculate', [TransactionPricingController::class, 'calculateCost']);

Route::middleware(['auth:sanctum'])->group(function () {
    // Core module routes can be added here
});
