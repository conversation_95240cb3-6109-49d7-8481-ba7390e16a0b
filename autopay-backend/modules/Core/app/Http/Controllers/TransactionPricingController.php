<?php

namespace Modules\Core\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Settings\TransactionPricingSettings;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Modules\Core\Helpers\ResponseHelper;
use Symfony\Component\HttpFoundation\Response;

class TransactionPricingController extends Controller
{
    protected TransactionPricingSettings $settings;

    public function __construct(TransactionPricingSettings $settings)
    {
        $this->settings = $settings;
    }

    /**
     * Get transaction pricing tiers
     */
    public function index(): Response
    {
        try {
            $tiers = $this->settings->getPricingTiers();

            return ResponseHelper::success(
                message: 'Lấy bảng giá giao dịch thành công',
                data: [
                    'pricing_tiers' => $tiers,
                ]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi x<PERSON>y ra khi lấy bảng giá giao dịch',
                data: null,
                httpCode: 500
            );
        }
    }

    /**
     * Update transaction pricing tiers
     */
    public function update(Request $request): Response
    {
        try {
            $validated = $request->validate([
                'pricing_tiers' => 'required|array|min:1',
                'pricing_tiers.*.name' => 'required|string|max:255',
                'pricing_tiers.*.name_en' => 'required|string|max:255',
                'pricing_tiers.*.min_transactions' => 'required|integer|min:0',
                'pricing_tiers.*.max_transactions' => 'nullable|integer|min:1',
                'pricing_tiers.*.price_per_transaction' => 'required|integer|min:0',
                'pricing_tiers.*.color_class' => 'required|string|max:50',
                'pricing_tiers.*.is_unlimited' => 'required|boolean',
            ]);

            // Validate tier logic
            $this->validateTierLogic($validated['pricing_tiers']);

            $this->settings->setPricingTiers($validated['pricing_tiers']);

            return ResponseHelper::success(
                message: 'Cập nhật bảng giá giao dịch thành công',
                data: [
                    'pricing_tiers' => $this->settings->getPricingTiers(),
                ]
            );
        } catch (ValidationException $e) {
            return ResponseHelper::error(
                message: 'Dữ liệu không hợp lệ',
                data: $e->errors(),
                httpCode: 422
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi xảy ra khi cập nhật bảng giá giao dịch',
                data: null,
                httpCode: 500
            );
        }
    }

    /**
     * Calculate cost for given transaction count
     */
    public function calculateCost(Request $request): Response
    {
        try {
            $validated = $request->validate([
                'transaction_count' => 'required|integer|min:0',
            ]);

            $calculation = $this->settings->calculateTotalCost($validated['transaction_count']);

            return ResponseHelper::success(
                message: 'Tính toán chi phí thành công',
                data: $calculation
            );
        } catch (ValidationException $e) {
            return ResponseHelper::error(
                message: 'Dữ liệu không hợp lệ',
                data: $e->errors(),
                httpCode: 422
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi xảy ra khi tính toán chi phí',
                data: null,
                httpCode: 500
            );
        }
    }

    /**
     * Reset pricing tiers to default
     */
    public function resetToDefault(): Response
    {
        try {
            $defaultTiers = $this->settings->getDefaultPricingTiers();
            $this->settings->setPricingTiers($defaultTiers);

            return ResponseHelper::success(
                message: 'Khôi phục bảng giá mặc định thành công',
                data: [
                    'pricing_tiers' => $defaultTiers,
                ]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi xảy ra khi khôi phục bảng giá mặc định',
                data: null,
                httpCode: 500
            );
        }
    }

    /**
     * Validate tier logic to ensure no gaps or overlaps
     */
    private function validateTierLogic(array $tiers): void
    {
        // Sort tiers by min_transactions
        usort($tiers, fn($a, $b) => $a['min_transactions'] <=> $b['min_transactions']);

        $expectedMin = 0;

        foreach ($tiers as $index => $tier) {
            // Check if min_transactions matches expected value
            if ($tier['min_transactions'] !== $expectedMin) {
                throw ValidationException::withMessages([
                    "pricing_tiers.{$index}.min_transactions" => 'Có khoảng trống trong bảng giá. Mức giao dịch tối thiểu phải là ' . $expectedMin,
                ]);
            }

            // Check if max_transactions is valid
            if ($tier['max_transactions'] !== null && $tier['max_transactions'] < $tier['min_transactions']) {
                throw ValidationException::withMessages([
                    "pricing_tiers.{$index}.max_transactions" => 'Số giao dịch tối đa phải lớn hơn hoặc bằng số giao dịch tối thiểu',
                ]);
            }

            // Update expected min for next tier
            if ($tier['is_unlimited'] || $tier['max_transactions'] === null) {
                // This should be the last tier
                if ($index !== count($tiers) - 1) {
                    throw ValidationException::withMessages([
                        "pricing_tiers.{$index}.is_unlimited" => 'Chỉ mức cuối cùng mới có thể không giới hạn',
                    ]);
                }
                break;
            } else {
                $expectedMin = $tier['max_transactions'] + 1;
            }
        }
    }
}
