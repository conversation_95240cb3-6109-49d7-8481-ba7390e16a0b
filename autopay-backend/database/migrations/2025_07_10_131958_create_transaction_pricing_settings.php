<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert default transaction pricing settings
        $defaultPricingTiers = [
            [
                'name' => 'Miễn phí',
                'name_en' => 'Free',
                'min_transactions' => 0,
                'max_transactions' => 100,
                'price_per_transaction' => 0,
                'color_class' => 'green',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Cơ bản',
                'name_en' => 'Basic',
                'min_transactions' => 101,
                'max_transactions' => 500,
                'price_per_transaction' => 500,
                'color_class' => 'default',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Tiết kiệm',
                'name_en' => 'Economy',
                'min_transactions' => 501,
                'max_transactions' => 1000,
                'price_per_transaction' => 400,
                'color_class' => 'default',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Ưu đãi',
                'name_en' => 'Premium',
                'min_transactions' => 1001,
                'max_transactions' => null,
                'price_per_transaction' => 300,
                'color_class' => 'default',
                'is_unlimited' => true,
            ],
        ];

        $setting = [
            'group' => 'transaction_pricing',
            'name' => 'pricing_tiers',
            'payload' => json_encode($defaultPricingTiers),
            'locked' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('settings')->updateOrInsert(
            ['group' => $setting['group'], 'name' => $setting['name']],
            $setting
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove transaction pricing settings
        DB::table('settings')->where('group', 'transaction_pricing')->delete();
    }
};
