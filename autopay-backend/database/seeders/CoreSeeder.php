<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use Modules\Subscription\Models\Feature;
use Modules\Subscription\Models\Plan;

class CoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Default features and plans are now created in the migration
        // This seeder can be used for additional data if needed

        // Example: Create additional features or plans here if needed
        // For now, keeping it empty as the migration handles the defaults
    }
}
