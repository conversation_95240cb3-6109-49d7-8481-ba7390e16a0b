'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { Loader2, Plus } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { createColumns } from './components/columns'
import CreatePlanModal from './components/create-plan-modal'
import DeletePlanDialog from './components/delete-plan-dialog'
import EditPlanModal from './components/edit-plan-modal'

export interface OrganizationPlan {
  id: string
  name: string
  alias?: string | null
  description: string | null
  grace_days: number
  periodicity: number
  periodicity_type: string
  price: number
  organization_id: string
  is_active: boolean
  quota?: number | null
  features: Array<{
    id: string
    name: string
    alias?: string | null
    consumable: boolean
    quota: boolean
    postpaid: boolean
    pivot: {
      charges: number | null
    }
  }>
  subscriptions?: Array<{
    id: string
    subscriber_id: string
    subscriber_type: string
  }>
  created_at: string
  updated_at: string
}

interface OrganizationPlansResponse {
  success: boolean
  code?: number
  locale?: string
  message?: string
  data: {
    data: OrganizationPlan[]
    current_page: number
    per_page: number
    total: number
    last_page: number
  }
}

interface OrganizationUsage {
  usage_stats: Array<{
    feature_id: string
    feature_name: string
    consumed: number
    remaining: number | null
    quota: number | null
    is_unlimited: boolean
    usage_percentage: number
  }>
  is_pay_as_you_go: boolean
  billable_usage: Array<{
    feature_id: string
    feature_name: string
    consumed: number
    rate: number
    total_cost: number
  }>
}

interface OrganizationUsageResponse {
  success: boolean
  code?: number
  locale?: string
  message?: string
  data: OrganizationUsage
}

export default function PlansPage() {
  const { user } = useUser()
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editingPlan, setEditingPlan] = useState<OrganizationPlan | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingPlan, setDeletingPlan] = useState<OrganizationPlan | null>(null)

  // Action handlers for plan operations
  const handleEditPlan = (plan: OrganizationPlan) => {
    setEditingPlan(plan)
    setEditModalOpen(true)
  }

  const handleToggleStatus = async (plan: OrganizationPlan) => {
    try {
      const response = await queryFetchHelper(
        `/organizations/${user?.current_organization?.id}/plans/${plan.id}/toggle-status`,
        {
          method: 'PATCH',
        }
      )

      if (response.success) {
        toast.success(response.message)
        refetchOrgPlans()
      } else {
        toast.error(response.message || 'Có lỗi xảy ra')
      }
    } catch (error) {
      toast.error('Không thể thay đổi trạng thái gói dịch vụ')
    }
  }

  const handleDeletePlan = (plan: OrganizationPlan): void => {
    setDeletingPlan(plan)
    setDeleteDialogOpen(true)
  }

  // Create columns with action handlers
  const columns = createColumns({
    onEdit: handleEditPlan,
    onToggleStatus: handleToggleStatus,
    onDelete: handleDeletePlan,
  })

  // Fetch organization plans
  const {
    data: orgPlansData,
    isLoading: orgPlansLoading,
    error: orgPlansError,
    refetch: refetchOrgPlans,
  } = useQuery<OrganizationPlansResponse>({
    queryKey: ['getOrganizationPlans', user?.current_organization?.id],
    queryFn: () => queryFetchHelper(`/organizations/${user?.current_organization?.id}/plans`),
    enabled: !!user?.current_organization?.id,
  })

  // Fetch organization usage statistics
  const {
    data: usageData,
    isLoading: usageLoading,
    error: usageError,
  } = useQuery<OrganizationUsageResponse>({
    queryKey: ['getOrganizationUsage', user?.current_organization?.id],
    queryFn: () =>
      queryFetchHelper(`/organizations/${user?.current_organization?.id}/subscriptions/organization/usage`),
    enabled: !!user?.current_organization?.id,
  })

  // Fetch transaction pricing tiers
  const {
    data: pricingData,
    isLoading: pricingLoading,
    error: pricingError,
  } = useQuery<{
    success: boolean
    data: {
      pricing_tiers: Array<{
        name: string
        name_en: string
        min_transactions: number
        max_transactions: number | null
        price_per_transaction: number
        color_class: string
        is_unlimited: boolean
      }>
    }
  }>({
    queryKey: ['getTransactionPricing'],
    queryFn: () => queryFetchHelper('/transaction-pricing'),
  })

  const renderOrganizationPlans = () => {
    if (orgPlansError) {
      return (
        <Alert variant="destructive">
          <AlertDescription>Không thể tải danh sách gói dịch vụ. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      )
    }

    // Only get plans data if orgPlansData exists, otherwise use empty array
    const plans = orgPlansData?.data?.data || []

    return (
      <DataTable
        columns={columns}
        data={plans}
        isLoading={orgPlansLoading || !orgPlansData}
        emptyMessage="Chưa có gói dịch vụ nào. Tạo gói dịch vụ đầu tiên để bắt đầu quản lý thành viên."
        loadingRows={3}
      />
    )
  }

  const renderUsageStats = () => {
    if (usageLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="mr-2 h-6 w-6 animate-spin" />
          <span>Đang tải thống kê sử dụng...</span>
        </div>
      )
    }

    if (usageError) {
      return (
        <Alert variant="destructive">
          <AlertDescription>Không thể tải thống kê sử dụng. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      )
    }

    const usage = usageData?.data

    // If no usage data, show default state with 0 usage
    if (!usage || !usage.usage_stats || usage.usage_stats.length === 0) {
      // Show default usage stats for pay-as-you-go
      const defaultUsageStats = [
        {
          feature_id: 'transactions',
          feature_name: 'Giao dịch',
          consumed: 0,
          remaining: null,
          quota: null,
          is_unlimited: true,
          usage_percentage: 0,
        },
      ]

      return (
        <>
          {/* Current Usage Stats - Default State */}
          <div>
            {defaultUsageStats.map((stat) => (
              <div
                key={stat.feature_id}
                className="flex items-center justify-between rounded-lg border p-3">
                <div>
                  <div className="text-sm font-semibold">Tổng {stat.feature_name}</div>
                  <div className="text-muted-foreground text-xs">Tổ chức của bạn đã sử dụng trong tháng này</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold">{stat.consumed.toLocaleString('vi-VN')}</div>
                </div>
              </div>
            ))}
          </div>

          <div className="h-4"></div>

          {/* Usage Levels Table - Always show */}
          <Card>
            <CardHeader className="px-4 pb-3">
              <CardTitle>Bảng giá giao dịch</CardTitle>
              <CardDescription className="text-xs">Phí giảm dần theo số lượng giao dịch</CardDescription>
            </CardHeader>
            <CardContent className="px-4">
              {pricingLoading ? (
                <div className="space-y-2">
                  <div className="text-muted-foreground grid grid-cols-3 gap-4 border-b pb-2 text-xs font-medium">
                    <span>Mức</span>
                    <span>Số giao dịch</span>
                    <span className="text-right">Giá/giao dịch</span>
                  </div>
                  <div className="space-y-2">
                    {[1, 2, 3, 4].map((i) => (
                      <div
                        key={i}
                        className="grid grid-cols-3 gap-4 rounded-lg border p-2 text-sm">
                        <div className="bg-muted h-4 w-16 animate-pulse rounded"></div>
                        <div className="bg-muted h-4 w-20 animate-pulse rounded"></div>
                        <div className="bg-muted ml-auto h-4 w-12 animate-pulse rounded"></div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : pricingError ? (
                <Alert variant="destructive">
                  <AlertDescription>Không thể tải bảng giá giao dịch. Vui lòng thử lại sau.</AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-2">
                  <div className="text-muted-foreground grid grid-cols-3 gap-4 border-b pb-2 text-xs font-medium">
                    <span>Mức</span>
                    <span>Số giao dịch</span>
                    <span className="text-right">Giá/giao dịch</span>
                  </div>
                  <div className="space-y-2">
                    {pricingData?.data?.pricing_tiers?.map((tier, index) => {
                      const isGreen = tier.color_class === 'green'
                      const rangeText = tier.is_unlimited
                        ? `${tier.min_transactions.toLocaleString()}+`
                        : `${tier.min_transactions.toLocaleString()} - ${tier.max_transactions?.toLocaleString()}`

                      return (
                        <div
                          key={index}
                          className={`grid grid-cols-3 gap-4 rounded-lg p-2 text-sm ${
                            isGreen ? 'bg-green-50' : 'border'
                          }`}>
                          <span className={`font-medium ${isGreen ? 'text-green-700' : ''}`}>{tier.name}</span>
                          <span>{rangeText}</span>
                          <span className={`text-right font-semibold ${isGreen ? 'text-green-700' : ''}`}>
                            {new Intl.NumberFormat('vi-VN', {
                              style: 'currency',
                              currency: 'VND',
                            }).format(tier.price_per_transaction)}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                  <div className="mt-3 rounded-lg bg-blue-50 p-3">
                    <div className="text-xs text-blue-700">
                      <strong>Ví dụ:</strong> Nếu bạn có 1,200 giao dịch trong tháng:
                      <br />
                      <br />
                      • 100 giao dịch đầu: 0₫
                      <br />
                      • 400 giao dịch tiếp: 400 × 500₫ = 200,000₫
                      <br />
                      • 500 giao dịch tiếp: 500 × 400₫ = 200,000₫
                      <br />
                      • 200 giao dịch cuối: 200 × 300₫ = 60,000₫
                      <br />
                      <br />
                      <strong>Tổng: 460,000₫</strong>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )
    }

    return (
      <>
        {/* Current Usage Stats */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Sử dụng hiện tại</CardTitle>
            <CardDescription className="text-xs">
              {usage.is_pay_as_you_go ? 'Thanh toán theo sử dụng thực tế' : 'Gói cố định'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {usage.usage_stats.map((stat) => (
                <div
                  key={stat.feature_id}
                  className="flex items-center justify-between rounded-lg border p-3">
                  <div>
                    <div className="text-sm font-medium">{stat.feature_name}</div>
                    <div className="text-muted-foreground text-xs">Đã sử dụng trong tháng này</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold">{stat.consumed.toLocaleString('vi-VN')}</div>
                    <div className="text-muted-foreground text-xs">giao dịch</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="h-4"></div>

        {/* Billable Usage (for pay-as-you-go) */}
        {usage.is_pay_as_you_go && usage.billable_usage.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Chi phí tháng này</CardTitle>
              <CardDescription className="text-xs">Phí tính theo số giao dịch thực tế</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {usage.billable_usage.map((billing) => (
                  <div
                    key={billing.feature_id}
                    className="flex items-center justify-between rounded-lg border p-3">
                    <div>
                      <div className="text-sm font-medium">{billing.feature_name}</div>
                      <div className="text-muted-foreground text-xs">
                        {billing.consumed.toLocaleString('vi-VN')} giao dịch × {billing.rate.toLocaleString('vi-VN')}₫
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold">{billing.total_cost.toLocaleString('vi-VN')}₫</div>
                    </div>
                  </div>
                ))}
                <div className="mt-3 border-t pt-3">
                  <div className="flex items-center justify-between">
                    <span className="text-base font-medium">Tổng chi phí:</span>
                    <span className="text-primary text-xl font-bold">
                      {usage.billable_usage
                        .reduce((sum, billing) => sum + billing.total_cost, 0)
                        .toLocaleString('vi-VN')}
                      ₫
                    </span>
                  </div>
                  <div className="text-muted-foreground mt-1 text-xs">Sẽ được tính vào hóa đơn cuối tháng</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="h-4"></div>

        {/* Usage Levels Table */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Bảng giá giao dịch</CardTitle>
            <CardDescription className="text-xs">Phí giảm dần theo số lượng giao dịch</CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            {pricingLoading ? (
              <div className="space-y-2">
                <div className="text-muted-foreground grid grid-cols-3 gap-4 border-b pb-2 text-xs font-medium">
                  <span>Mức</span>
                  <span>Số giao dịch</span>
                  <span className="text-right">Giá/giao dịch</span>
                </div>
                <div className="space-y-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="grid grid-cols-3 gap-4 rounded-lg border p-2 text-sm">
                      <div className="bg-muted h-4 w-12 animate-pulse rounded"></div>
                      <div className="bg-muted h-4 w-16 animate-pulse rounded"></div>
                      <div className="bg-muted ml-auto h-4 w-10 animate-pulse rounded"></div>
                    </div>
                  ))}
                </div>
              </div>
            ) : pricingError ? (
              <Alert variant="destructive">
                <AlertDescription>Không thể tải bảng giá giao dịch.</AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-2">
                <div className="text-muted-foreground grid grid-cols-3 gap-4 border-b pb-2 text-xs font-medium">
                  <span>Mức</span>
                  <span>Số giao dịch</span>
                  <span className="text-right">Giá/giao dịch</span>
                </div>
                <div className="space-y-2">
                  {pricingData?.data?.pricing_tiers?.map((tier, index) => {
                    const isGreen = tier.color_class === 'green'
                    const rangeText = tier.is_unlimited
                      ? `${tier.min_transactions.toLocaleString()}+`
                      : `${tier.min_transactions.toLocaleString()} - ${tier.max_transactions?.toLocaleString()}`

                    return (
                      <div
                        key={index}
                        className={`grid grid-cols-3 gap-4 rounded-lg p-2 text-sm ${
                          isGreen ? 'bg-green-50' : 'border'
                        }`}>
                        <span className={`font-medium ${isGreen ? 'text-green-700' : ''}`}>{tier.name}</span>
                        <span>{rangeText}</span>
                        <span className={`text-right font-semibold ${isGreen ? 'text-green-700' : ''}`}>
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                          }).format(tier.price_per_transaction)}
                        </span>
                      </div>
                    )
                  })}
                </div>
                <div className="mt-3 rounded-lg bg-blue-50 p-3">
                  <div className="text-xs text-blue-700">
                    <strong>Ví dụ:</strong> Nếu bạn có 1,200 giao dịch trong tháng:
                    <br />
                    • 100 giao dịch đầu: 0₫
                    <br />
                    • 400 giao dịch tiếp: 400 × 500₫ = 200,000₫
                    <br />
                    • 500 giao dịch tiếp: 500 × 400₫ = 200,000₫
                    <br />
                    • 200 giao dịch cuối: 200 × 300₫ = 60,000₫
                    <br />
                    <strong>Tổng: 460,000₫</strong>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Quản lý Gói dịch vụ</h3>
        <p className="text-muted-foreground text-sm">Tạo và quản lý các gói dịch vụ cho thành viên trong tổ chức</p>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column - Organization Plans */}
        <div className="space-y-4 lg:col-span-2">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-base font-medium">Gói tổ chức</h4>
              <p className="text-muted-foreground text-sm">{orgPlansData?.data?.total || 0} gói dịch vụ</p>
            </div>
            <Button
              onClick={() => setCreateModalOpen(true)}
              size="sm">
              <Plus className="size-4" />
              Tạo gói mới
            </Button>
          </div>

          {renderOrganizationPlans()}
        </div>

        {/* Right Column - Usage Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Thống kê sử dụng</CardTitle>
            <CardDescription className="text-sm">Quota và chi phí hiện tại</CardDescription>
          </CardHeader>
          <CardContent>{renderUsageStats()}</CardContent>
        </Card>
      </div>

      {/* Create Plan Modal */}
      <CreatePlanModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={() => {
          // Refetch organization plans after successful creation
          refetchOrgPlans()
        }}
      />

      {/* Edit Plan Modal */}
      <EditPlanModal
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        plan={editingPlan}
        onSuccess={() => {
          // Refetch organization plans after successful update
          refetchOrgPlans()
        }}
      />

      {/* Delete Plan Dialog */}
      <DeletePlanDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        plan={deletingPlan}
        onSuccess={() => {
          // Refetch organization plans after successful deletion
          refetchOrgPlans()
        }}
      />
    </div>
  )
}
