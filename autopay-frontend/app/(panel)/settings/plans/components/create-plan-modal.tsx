'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

const formSchema = z.object({
  name: z.string().min(1, 'Tên gói dịch vụ là bắt buộc'),
  description: z.string().optional(),
  grace_days: z.number().min(0).max(365),
  periodicity: z.number().min(1),
  periodicity_type: z.enum(['day', 'week', 'month', 'year']),
  quota: z.number().min(0).optional(),
  price: z.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
})

type FormData = z.infer<typeof formSchema>

interface CreatePlanModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export default function CreatePlanModal({ open, onOpenChange, onSuccess }: CreatePlanModalProps) {
  const { user } = useUser()
  const queryClient = useQueryClient()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      grace_days: 0,
      periodicity: 1,
      periodicity_type: 'month',
      quota: undefined,
      price: 0,
    },
  })

  // Create plan mutation
  const createPlanMutation = useMutation({
    mutationFn: async (data: FormData) => {
      return queryFetchHelper(`/organizations/${user?.current_organization?.id}/plans`, {
        method: 'POST',
        body: JSON.stringify(data),
      })
    },
    onSuccess: () => {
      toast.success('Tạo gói dịch vụ thành công!')
      queryClient.invalidateQueries({ queryKey: ['getOrganizationPlans'] })
      onSuccess?.()
      onOpenChange(false)
      form.reset()
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tạo gói dịch vụ')
    },
  })

  const onSubmit = (data: FormData) => {
    createPlanMutation.mutate(data)
  }

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Tạo gói dịch vụ mới</DialogTitle>
          <DialogDescription>Tạo gói dịch vụ tùy chỉnh cho thành viên trong tổ chức</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên gói dịch vụ</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ví dụ: Gói Cơ bản"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mô tả</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Mô tả gói dịch vụ (tùy chọn)"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="periodicity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Chu kỳ</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="periodicity_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Đơn vị thời gian</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn đơn vị" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="day">Ngày</SelectItem>
                          <SelectItem value="week">Tuần</SelectItem>
                          <SelectItem value="month">Tháng</SelectItem>
                          <SelectItem value="year">Năm</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="quota"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quota</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="Không giới hạn"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormDescription>Số lượng sử dụng tối đa (để trống = không giới hạn)</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="grace_days"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Thời gian gia hạn (ngày)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="365"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>Số ngày gia hạn sau khi gói dịch vụ hết hạn</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giá (VNĐ)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="1000"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>Giá của gói dịch vụ tính theo chu kỳ</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={createPlanMutation.isPending}>
                {createPlanMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Tạo gói dịch vụ
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
